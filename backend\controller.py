import os
import sys
import uvicorn

# 添加项目根目录到 Python 路径
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
if ROOT_DIR not in sys.path:
    sys.path.append(ROOT_DIR)

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import asyncio

# 导入搜索服务
from service.search import semantic_search_hybrid, deep_semantic_search_hybrid

app = FastAPI(title="BOM 匹配搜索 API", version="1.0.0")
# 测试阶段：允许更多域名访问
origins = [
    "http://localhost:5173",  # Vite 默认端口
    "https://szu.axsight.top",  # 你的域名
    "http://szu.axsight.top",   # HTTP 版本（如果需要）
    # 测试阶段可以考虑添加通配符，但生产环境建议移除
    # "*"  # 允许所有域名（仅测试用，生产环境请删除此行）
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型
class SearchRequest(BaseModel):
    query: str
    limit: Optional[int] = 32
    folder_filter: Optional[str] = ""
    deep_search: Optional[bool] = False

# 响应模型
class SearchResult(BaseModel):
    primary_key: str
    data: str
    distance: float
    score: Optional[float] = None

class SearchResponse(BaseModel):
    results: List[SearchResult]
    total: int
    query: str
    search_type: str

@app.get("/hello")
def hello():
    return {"message": "world"}

@app.post("/search", response_model=SearchResponse)
async def search_bom(request: SearchRequest):
    """
    BOM 搜索接口
    """
    try:
        if not request.query.strip():
            raise HTTPException(status_code=400, detail="查询内容不能为空")

        # 根据是否开启深度搜索选择不同的搜索方法
        if request.deep_search:
            results = await deep_semantic_search_hybrid(
                query=request.query,
                limit=request.limit,
                folder_filter=request.folder_filter
            )
            search_type = "deep_semantic"
        else:
            results = await semantic_search_hybrid(
                query=request.query,
                limit=request.limit,
                folder_filter=request.folder_filter
            )
            search_type = "semantic"

        # 转换结果格式
        search_results = []
        for result in results:
            search_result = SearchResult(
                primary_key=result.get("primary_key", ""),
                data=result.get("data", ""),
                distance=result.get("distance", 0.0),
                score=result.get("score") if "score" in result else None
            )
            search_results.append(search_result)

        return SearchResponse(
            results=search_results,
            total=len(search_results),
            query=request.query,
            search_type=search_type
        )

    except Exception as e:
        print(f"搜索错误: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@app.get("/")
def root():
    return {"message": "BOM 匹配搜索 API", "version": "1.0.0"}

if __name__ == "__main__":
    file_path = os.path.basename(__file__)
    module_name = file_path[:-3]  # 移除 .py 后缀
    uvicorn.run(
        f"{module_name}:app",
        host="0.0.0.0",
        port=8848,
        reload=False,
        log_level="debug"  # 启用日志输出
    )