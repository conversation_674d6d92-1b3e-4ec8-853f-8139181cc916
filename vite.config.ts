// vite.config.ts
import path from "path";
import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";
import { defineConfig, type PluginOption } from "vite";

export default defineConfig({
  server: {
    allowedHosts: ['szu.axsight.top'],
    watch: {
      ignored: [
        '**/backend/**',
        '**/public/**',
        '**/node_modules/**'
      ]
    }
  },
  plugins: [react(), ...(tailwindcss() as unknown as PluginOption[])],
  resolve: {
    alias: { "@": path.resolve(__dirname, "./src") },
  },
});