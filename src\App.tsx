import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>H<PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Search, Loader2 } from "lucide-react";
import { api } from "@/lib/api";

interface SearchResult {
  primary_key: string;
  data: string;
  distance: number;
  score?: number;
}

interface SearchResponse {
  results: SearchResult[];
  total: number;
  query: string;
  search_type: string;
}

export default function App() {
  const [query, setQuery] = useState<string>("");
  const [deepSearch, setDeepSearch] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [searchInfo, setSearchInfo] = useState<{
    total: number;
    query: string;
    search_type: string;
  } | null>(null);

  const handleSearch = async () => {
    if (!query.trim()) return;

    setLoading(true);
    try {
      const response = await api<SearchResponse>("/search", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: query.trim(),
          limit: 88,
          folder_filter: "",
          deep_search: deepSearch,
        }),
      });

      // 按照评分从高到低排序（如果有评分的话）
      const sortedResults = response.results.sort((a, b) => {
        // 如果都有评分，按评分排序
        if (a.score !== undefined && b.score !== undefined) {
          return b.score - a.score;
        }
        // 如果只有一个有评分，有评分的排在前面
        if (a.score !== undefined && b.score === undefined) {
          return -1;
        }
        if (a.score === undefined && b.score !== undefined) {
          return 1;
        }
        // 如果都没有评分，按相似度排序（distance 越小越相似）
        return a.distance - b.distance;
      });

      setResults(sortedResults);
      setSearchInfo({
        total: response.total,
        query: response.query,
        search_type: response.search_type,
      });
    } catch (error) {
      console.error("搜索失败:", error);
      setResults([]);
      setSearchInfo(null);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const parseData = (dataStr: string) => {
    try {
      return JSON.parse(dataStr);
    } catch {
      return { content: dataStr };
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-8">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              BOM Match
            </h1>
            <p className="text-lg text-gray-600">
              智能 BOM 匹配搜索引擎
            </p>
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="输入零件型号、品牌、参数等信息进行搜索..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                className="pl-10 pr-4 py-3 text-lg"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  id="deep-search"
                  checked={deepSearch}
                  onCheckedChange={setDeepSearch}
                />
                <Label htmlFor="deep-search" className="text-sm">
                  开启深度匹配 (使用 AI 智能评分)
                </Label>
              </div>

              <Button
                onClick={handleSearch}
                disabled={loading || !query.trim()}
                className="px-8"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    搜索中...
                  </>
                ) : (
                  "搜索"
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="max-w-6xl mx-auto px-4 py-8">
        {searchInfo && (
          <div className="mb-6">
            <div className="flex items-center gap-4 text-sm text-gray-600 flex-wrap">
              <span>找到 {searchInfo.total} 个结果</span>
              <Badge variant={searchInfo.search_type === "deep_semantic" ? "default" : "secondary"}>
                {searchInfo.search_type === "deep_semantic" ? "深度匹配" : "语义搜索"}
              </Badge>
              {searchInfo.search_type === "deep_semantic" && (
                <Badge variant="outline" className="text-xs">
                  按AI评分排序
                </Badge>
              )}
              <span>查询: "{searchInfo.query}"</span>
            </div>
          </div>
        )}

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {results.map((result, index) => {
            const data = parseData(result.data);
            return (
              <Card key={result.primary_key} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm font-medium text-gray-600">
                      #{index + 1}
                    </CardTitle>
                    <div className="flex gap-2">
                      {result.score && (
                        <Badge
                          variant={result.score >= 8 ? "default" : result.score >= 6 ? "secondary" : "outline"}
                          className="text-xs font-semibold"
                        >
                          AI评分: {result.score.toFixed(1)}
                        </Badge>
                      )}
                      <Badge variant="outline" className="text-xs">
                        相似度: {(1 - result.distance).toFixed(3)}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-2">
                    <div className="text-xs text-gray-500 mb-2">
                      ID: {result.primary_key}
                    </div>
                    {Object.entries(data).map(([key, value]) => (
                      <div key={key} className="flex flex-wrap gap-1 items-start">
                        <span className="text-xs font-bold text-gray-700 capitalize shrink-0">
                          {key}:
                        </span>
                        <span className="text-sm text-gray-900 break-all word-break-all overflow-wrap-anywhere">
                          {String(value)}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {results.length === 0 && searchInfo && (
          <div className="text-center py-12">
            <p className="text-gray-500">未找到匹配的结果</p>
          </div>
        )}
      </div>
    </div>
  );
}